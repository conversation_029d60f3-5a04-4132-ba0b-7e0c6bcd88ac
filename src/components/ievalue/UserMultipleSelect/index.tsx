import {Select, Spin} from 'antd';
import type {SelectProps} from 'antd/es/select';
import debounce from 'lodash/debounce';
import {useMemo, useRef, useState, ReactNode} from 'react';
import {apiGetUserByQuery} from '@/api/ievalue/user';

export interface DebounceSelectProps<ValueType = any>
    extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
    fetchOptions: (search: string) => Promise<ValueType[]>;
    debounceTimeout?: number;
}

interface UserValue {
    label: string;
    value: string;
}
function DebounceSelect<
    ValueType extends {key?: string, label: ReactNode, value: string | number} = any
>({fetchOptions, debounceTimeout = 800, labelInValue, ...props}: DebounceSelectProps<ValueType>) {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState<ValueType[]>([]);
    const fetchRef = useRef(0);
    const debounceFetcher = useMemo(
        () => {
            const loadOptions = (value: string) => {
                fetchRef.current += 1;
                const fetchID = fetchRef.current;
                setOptions([]);
                setFetching(true);

                fetchOptions(value).then(newOptions => {
                    if (fetchID !== fetchRef.current) {
                    // for fetch callback order
                        return;
                    }

                    setOptions(newOptions);
                    setFetching(false);
                });
            };

            return debounce(loadOptions, debounceTimeout);
        },
        [fetchOptions, debounceTimeout]
    );
    return (
        <Select
            showSearch
            labelInValue={labelInValue === undefined ? true : labelInValue}
            filterOption={false}
            onSearch={debounceFetcher}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            {...props}
            options={options}
        />
    );
}

export async function fetchUserList(username: string): Promise<any> {
    if (username === '') {
        return [];
    }
    return apiGetUserByQuery({userID: username})
        .then(users => {
            return users.map(user => {
                return {
                    ...user,
                    value: user.username,
                    label: `${user.name}(${user.username})-${user.departmentName}`,
                };
            });
        })
        .catch((): AccountFEModel[] => []);
}

export type Nil = undefined | null;

/**
 * 前端使用的账号类型
 */
export interface AccountFEModel {
    departmentName: string;
    email: string;
    name: string;
    username: string;
}

interface UserSelectProps extends SelectProps {
    value?: UserValue[];
    onChange?: (newValue: any, options?: any) => void;
    mode: any;
    labelInValue?: boolean;
}
export function UserMultipleSelect({onChange, mode, labelInValue, ...props}: UserSelectProps) {
    return (
        <DebounceSelect
            mode={mode}
            labelInValue={labelInValue}
            placeholder={props.placeholder || '请选择项目管理员'}
            {...props}
            fetchOptions={fetchUserList}
            onChange={onChange}
            style={props.style || {width: mode === 'multiple' ? '100%' : '280px'}}
        />
    );
}
