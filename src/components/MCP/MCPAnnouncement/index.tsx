import {Flex, Typography} from 'antd';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {IconAlert, IconClose} from '@/icons/mcp';

const Container = styled(Flex)`
  height: 40px;
  background-color: #E5F2FF;
  font-size: 14px;
  color: #000;
`;

const leftIconCss = css`
  margin-left: 24px;
  margin-right: 16px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  flex-grow: 0;
`;

const rightIconCss = css`
  margin-left: 12px;
  margin-right: 12px;
  flex-shrink: 0;
  flex-grow: 0;
  cursor: pointer;
`;

const textCss = css`
  flex: 1;
`;

interface Props {
  text: string;
  className?: string;
  closeAnnouncement: () => void;
  visible: boolean;
}

export const MCPAnnouncement = ({text, className, visible, closeAnnouncement}: Props) => {
    if (!visible) {
        return null;
    }
    return (
        <Container align="center" className={className}>
            <IconAlert className={leftIconCss} />
            <Typography.Text className={textCss} ellipsis={{tooltip: text}}>{text}</Typography.Text>
            <IconClose className={rightIconCss} onClick={closeAnnouncement} />
        </Container>
    );
};
