import {Select} from 'antd';
import {useDebouncedValue, useRequestCallback} from 'huse';
import {useEffect, useState} from 'react';
import {apiGetEmailGroup} from '@/api/mcp';
import {fetchUserList} from '../ievalue/UserMultipleSelect';

interface EmailGroupSelectProps {
    value?: string[];
    onChange?: (value: string[]) => void;
    widthUser?: boolean;
}

// apiGetEmailGroup
// fetchUserList

const fetchEmailGroupAndUser = (prefix: string) => {
    const emailGroupPromise = apiGetEmailGroup({prefix});
    const userListPromise = fetchUserList(prefix);
    // @ts-ignore
    return Promise.all([emailGroupPromise.catch(() => []), userListPromise.catch(() => [])]).then(([emails, users]) => {
        return [
            ...emails.map(email => ({
                label: email,
                value: email,
            })),
            ...users,
        ];
    });
};

const onlyFetchEmailGroup = (prefix: string) => {
    return apiGetEmailGroup({prefix}).then(emails => {
        return emails.map(email => ({
            label: email,
            value: email,
        }));
    });
};

export const EmailGroupSelect = ({value, onChange, widthUser}: EmailGroupSelectProps) => {
    const [searchWord, setSearchWord] = useState<string>();
    const searchValueWidthDebounce = useDebouncedValue(searchWord, 500);
    const fetch = widthUser ? fetchEmailGroupAndUser : onlyFetchEmailGroup;
    const [refresh, {data}] = useRequestCallback(fetch, searchValueWidthDebounce);

    useEffect(
        () => {
            if (searchValueWidthDebounce && searchValueWidthDebounce.length > 0) {
                refresh();
            }
        },
        [refresh, searchValueWidthDebounce]
    );

    return (
        <Select
            placeholder="请输入邮箱或邮件组，指定可见范围"
            mode="multiple"
            value={value}
            onChange={onChange}
            onSearch={setSearchWord}
            options={data ?? []}
            showSearch
            allowClear
        />
    );
};
