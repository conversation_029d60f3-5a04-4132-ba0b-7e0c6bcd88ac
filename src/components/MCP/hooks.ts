/* eslint-disable max-lines */
import {useParams} from 'react-router-dom';
import {useRequest} from 'huse';
import {useCallback, useState} from 'react';
import {usePaginationSearchObj} from '@/hooks/ievalue/dashboard';
import {apiGetMCPWorkspaces, apiGetWorkspaceApplications, GetMCPWorkspacesParam} from '@/api/mcp';
import {useMCPSpaceInfoRegion} from '@/regions/mcp/mcpSpace';
import {useMCPApplicationInfoRegion} from '@/regions/mcp/mcpApplication';

/**
 * @description 从url获取MCP的spaceId
 */
export function useMCPWorkspaceId() {
    const {workspaceId} = useParams();
    if (!workspaceId) {
        return undefined;
    }
    return Number(workspaceId);
}

export function useMCPApplicationId() {
    const {applicationId} = useParams();
    if (!applicationId) {
        throw new Error('applicationId is required');
    }
    return +applicationId;
}

export function useMCPSpaceAppPaginationList() {
    const workspaceId = useMCPWorkspaceId();
    const {pn, size, setPn, setSize, searchObj, setSearchObj} = usePaginationSearchObj({}, 100);
    const {
        data,
        pending,
        refresh,
    } = useRequest(apiGetWorkspaceApplications, {
        pn,
        size,
        workspaceId,
        ...searchObj,
    });

    const appListRes = data?.records || [];
    return {
        dataSource: appListRes,
        pagination: {
            current: pn,
            pageSize: size,
            total: data?.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
            showSizeChanger: true,
        },
        refresh,
        pending,
        searchObj,
        setSearchObj,
    };
}

export function useMCPWorkspaceInfo() {
    const workspaceId = useMCPWorkspaceId();
    const mcpSpaceInfo = useMCPSpaceInfoRegion(workspaceId);
    return mcpSpaceInfo;
}

export function useMCPApplicationInfo() {
    const applicationId = useMCPApplicationId();
    const mcpApplicationInfo = useMCPApplicationInfoRegion(applicationId);
    return mcpApplicationInfo;
}

export function useMCPWorkspaceList(initValue: GetMCPWorkspacesParam) {
    const [keywords, setKeywords] = useState(initValue?.keywords || '');
    const [mine, setMine] = useState(initValue.mine);
    const {data, refresh} = useRequest(apiGetMCPWorkspaces, {
        mine,
        keywords,
        size: 300,
    });

    const workspaceList = data?.records || [];

    return {
        workspaceList,
        refresh,
        keywords,
        setKeywords,
        mine,
        setMine,
    } as const;
}

export function useMCPServerId() {
    const {mcpId} = useParams();
    if (mcpId) {
        return Number(mcpId);
    }
}

export const useLoadMore = <T>(
    api: (params: {current: number, limit: number}) => Promise<{records: T[], total: number}>,
    limit: number = 10
) => {
    const [current, setCurrent] = useState(1);
    const [list, setList] = useState<T[]>([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);

    const loadMore = useCallback(
        async () => {
            if (loading) {
                return;
            }
            setLoading(true);
            try {
                setCurrent(current + 1);
                const data = await api({current: current + 1, limit});
                setList(list.concat(data.records));
                setTotal(data.total);
            }
            catch {
            // do nothing
            }
            finally {
                setLoading(false);
            }
        },
        [loading, api, current, list, limit]
    );
    const refresh = useCallback(
        async () => {
            try {
                setCurrent(1);
                setLoading(true);
                const data = await api({current: 1, limit});
                setList(data.records);
                setTotal(data.total);
            }
            catch {
                setList([]);
                setTotal(0);
            }
            finally {
                setLoading(false);
            }
        },
        [api, limit]
    );

    const updateItem = useCallback(
        (itemId: number | string, updater: (item: T) => T) => {
            setList(prevList => {
                const newList = [...prevList];
                const index = newList.findIndex(item => (item as any).id === itemId);
                if (index !== -1) {
                    newList[index] = updater(newList[index]);
                }
                return newList;
            });
        },
        []
    );

    return {
        total,
        list,
        loading,
        loadMore,
        refresh,
        updateItem,
    };
};
