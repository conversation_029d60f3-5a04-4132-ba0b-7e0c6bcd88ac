import {Form} from 'antd';
import constate from 'constate';
import {useCallback, useEffect, useState} from 'react';
import {AxiosError} from 'axios';
import {message} from '@panda-design/components';
import {apiGetMCPToolItem, apiPostMCPToolDebug} from '@/api/mcp';
import {BaseParam, getBaseParamType} from '@/types/mcp/mcp';
import {useMCPGlobalVarsContext} from './MCPServerConfigProvider';

interface Props {
    toolId?: number;
    mcpId?: number;
    toolKey?: string;
}

interface DebugResponse {
    type: 'params_missing' |
    'server_params_invalid' |
    'server_params_missing' |
    'tool_run_error' |
    'tool_params_invalid' |
    'success';
    info?: string;
    data?: any;
}

const formateParams = (params: BaseParam[]) => {
    const result = params.map(param => {
        let value = param.value;
        const type = getBaseParamType(param.dataType);
        if (type === 'date') {
            value = (value as unknown as Date).getTime().toString();
        }
        return {
            ...param,
            value,
        };
    });
    return result;
};

export const [MCPToolDebugProvider, useMCPToolDebugContext] = constate(({toolId, mcpId, toolKey}: Props) => {
    const {validateGlobalVars, globalVarsFormInstance, globalVars} = useMCPGlobalVarsContext();
    const [toolParams, setToolParams] = useState<BaseParam[]>([]);
    const [debugLoading, setDebugLoading] = useState(false);
    // 后端保证接口返回的值要么字符串，要么是JSON对象
    const [debugResult, setDebugResult] = useState<string | object | null>(null);
    const [form] = Form.useForm();
    const runDebug = useCallback(
        async (): Promise<DebugResponse> => {
            if (!toolKey || !mcpId) {
                window.console.error('toolKey or mcpId is undefined');
                return {
                    type: 'params_missing',
                    info: 'toolKey or mcpId is undefined',
                };
            }
            const valid = await validateGlobalVars();
            if (!valid) {
                message.error('请先进行服务配置！');
                return {
                    type: 'server_params_missing',
                };
            }
            setDebugLoading(true);
            try {
                const formValues = await form.validateFields();
                const newToolParams = toolParams.map(tool => {
                    const value = formValues[tool.name];
                    return {
                        ...tool,
                        value,
                    };
                });
                const serverParams = globalVarsFormInstance.getFieldsValue();
                // TODO 后端承诺该接口传的serverParams之后会被自动保存下来。
                const res = await apiPostMCPToolDebug({
                    mcpServerId: mcpId,
                    toolKey,
                    serverParams: globalVars.map(v => ({
                        ...v,
                        value: serverParams[v.name],
                    })),
                    toolParams: formateParams(newToolParams),
                });
                setDebugResult(res);
            } catch (e) {
                if (e instanceof AxiosError) {
                    message.error(e.response?.data?.msg || '工具运行失败');
                    return {
                        type: 'tool_run_error',
                        info: e.response?.data?.msg,
                    };
                } else {
                    const errorFieldName = e.errorFields[0].name.pop() as string;
                    return {
                        type: 'server_params_invalid',
                        info: `参数${errorFieldName}为必填项！`,
                        data: {
                            errorFieldName,
                        },
                    };
                }
            } finally {
                setDebugLoading(false);
            }
        },
        [toolKey, mcpId, validateGlobalVars, form, toolParams, globalVarsFormInstance, globalVars]
    );

    const refreshToolParams = useCallback(
        () => {
            setDebugResult(null);
            apiGetMCPToolItem({mcpServerId: mcpId, toolId}).then(res => {
                setToolParams(res.toolParams.toolParams ?? []);
                const values = res.toolParams.toolParams.reduce((acc, curr) => {
                    acc[curr.name] = curr.value;
                    return acc;
                }, {} as {[key: string]: string});
                form.setFieldsValue(values);
            });
        },
        [mcpId, toolId, form]
    );

    useEffect(
        () => {
            if (toolId && mcpId) {
                refreshToolParams();
            }
        },
        [toolId, mcpId, refreshToolParams]
    );

    return {
        runDebug,
        toolDebugFormInstance: form,
        debugLoading,
        debugResult,
        toolParams,
        refreshToolParams,
    };
});
