import constate from 'constate';
import {useDebouncedCallback, useDerivedState} from 'huse';
import {useCallback} from 'react';
import {apiGetSquareServerList} from '@/api/mcp';
import {SpaceLabel} from '@/types/mcp/mcp';
import {FilterValues, TabValues} from '../MCPServerFilter';
import {ALL_LABELS, OTHER_LABELS} from '../MCPServerFilter/LabelsFilterContent';
import {ServerType, ServerProtocol, Order} from '../MCPServerFilter/constant';

interface ZoneId {
    zoneId?: string;
}

interface PlatformType {
    platformType: string; // 'hub' | 'zone';
}

type SearchParams = FilterValues & TabValues & ZoneId & PlatformType;

const formatSearchParams = (searchParams?: SearchParams) => {
    const formated: any = {
        ...(searchParams.tab !== 'all' ? {[searchParams.tab]: true} : {}),
        labels: searchParams.labels?.includes(ALL_LABELS)
            ? undefined
            : searchParams.labels?.includes(OTHER_LABELS)
                ? '-1'
                : searchParams.labels?.map(label => label).join(','),
        keywords: searchParams?.keywords?.trim() || undefined,
        zoneId: searchParams.zoneId !== undefined ? Number(searchParams.zoneId) : undefined,
        serverSourceType: searchParams.serverSourceType === ServerType.ALL
            ? [ServerType.LOCAL, ServerType.REMOTE, ServerType.STANDARD].join(',') : searchParams.serverSourceType,
        serverProtocolType: searchParams.serverProtocolType === ServerProtocol.ALL
            ? [ServerProtocol.SSE, ServerProtocol.STREAMABLE_HTTP, ServerProtocol.STDIO].join(',')
            : searchParams.serverProtocolType,
        platformType: searchParams.platformType,

    };
    if ([Order.DEFAULT, Order.POPULAR].includes(searchParams.order)) {
        // TODO 目前还不支持这两项，先占个位
    } else if ([Order.COMMENT, Order.POPULAR, Order.USE, Order.NEWEST, Order.VIEW].includes(searchParams.order)) {
        formated[searchParams.order] = 'DESC';
    }
    return formated;
};

interface Props{
    initParams: SearchParams;
    fetchScenes?: () => Promise<SpaceLabel[]>;
}

export const [
    FetchMCPServersProvider,
    useFetchMCPServersContext,
] = constate(({initParams, fetchScenes}: Props) => {
    const [searchParams, setSearchParams] = useDerivedState<SearchParams>(initParams);
    const fetchServers = useCallback(
        async (pagination: { current: number, limit: number }) => {
            return apiGetSquareServerList({
                size: pagination.limit,
                pn: pagination.current,
                ...formatSearchParams(searchParams),
            });
        },
        [searchParams]
    );
    const _setSearchParams = useCallback(
        (newParams: Partial<SearchParams>) => {
            setSearchParams(p => {
                return {
                    ...p,
                    ...newParams,
                };
            });
        },
        [setSearchParams]
    );
    const changeSearchParams = useDebouncedCallback(_setSearchParams, 300);

    return {
        api: fetchServers,
        changeSearchParams,
        fetchScenes,
    };
});
