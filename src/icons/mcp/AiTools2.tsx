import type { SVGProps } from "react";
const SvgAiTools2 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 56 56"
        {...props}
    >
        <rect
            width={29.341}
            height={33.673}
            x={12.108}
            y={6.901}
            fill="url(#aiTools2_svg__a)"
            stroke="url(#aiTools2_svg__b)"
            strokeWidth={0.2}
            rx={3.9}
            transform="rotate(-5.094 12.108 6.901)"
        />
        <foreignObject width={49} height={40} x={4} y={10}>
            <div
                style={{
                    backdropFilter: "blur(1px)",
                    clipPath: "url(#aiTools2_svg__c)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={2} filter="url(#aiTools2_svg__d)">
            <path
                fill="#D4EBFF"
                fillOpacity={0.6}
                d="M6 20c0-2.8 0-4.2.545-5.27a5 5 0 0 1 2.185-2.185C9.8 12 11.2 12 14 12h9.02c.842 0 1.263 0 1.659.064a5 5 0 0 1 2.675 1.337c.288.279.54.615 1.046 1.289.49.653.734.98 1.014 1.251A5 5 0 0 0 32 17.276c.383.07.791.08 1.607.102l9.598.246c2.735.07 4.103.105 5.145.66a5 5 0 0 1 2.122 2.177c.528 1.056.528 2.424.528 5.16V40c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185C47.2 48 45.8 48 43 48H14c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C6 44.2 6 42.8 6 40z"
            />
            <path
                stroke="url(#aiTools2_svg__e)"
                strokeWidth={0.2}
                d="M14 12.1h9.02c.845 0 1.257 0 1.643.062.987.16 1.902.618 2.621 1.312.281.27.528.6 1.035 1.276.488.65.739.984 1.025 1.263a5.1 5.1 0 0 0 2.637 1.361c.393.072.81.083 1.624.104l9.597.246c1.37.035 2.39.06 3.2.147.807.086 1.396.232 1.9.501a4.9 4.9 0 0 1 2.08 2.134c.257.511.387 1.103.452 1.913.066.81.066 1.832.066 3.202V40c0 1.401 0 2.447-.068 3.275s-.202 1.431-.466 1.95a4.9 4.9 0 0 1-2.141 2.141c-.519.264-1.122.398-1.95.466S44.402 47.9 43 47.9H14c-1.402 0-2.447 0-3.275-.068s-1.431-.202-1.95-.466a4.9 4.9 0 0 1-2.141-2.141c-.264-.519-.398-1.122-.466-1.95S6.1 41.402 6.1 40V20c0-1.401 0-2.447.068-3.275s.202-1.431.466-1.95a4.9 4.9 0 0 1 2.141-2.141c.519-.264 1.122-.398 1.95-.466S12.599 12.1 14 12.1Z"
            />
        </g>
        <path
            fill="#000"
            d="M35.128 26.65c3.135.996 4.912 4.358 3.903 7.535-.995 3.135-4.4 4.898-7.534 3.903-1.186-.376-2.223-1.078-2.94-2.052-.3-.375-.21-.953.166-1.254a.87.87 0 0 1 .768-.147c.187.05.36.156.484.312a4.26 4.26 0 0 0 2.06 1.447c2.202.7 4.602-.544 5.301-2.747.7-2.203-.543-4.603-2.746-5.302-2.203-.7-4.603.544-5.302 2.747-.996 3.134-4.4 4.898-7.535 3.903-3.134-.996-4.898-4.4-3.903-7.535.996-3.134 4.4-4.898 7.535-3.903a6 6 0 0 1 2.81 1.872c.3.375.265.923-.123 1.266a.9.9 0 0 1-.802.17.95.95 0 0 1-.465-.292 4.1 4.1 0 0 0-1.958-1.322c-2.203-.699-4.603.544-5.303 2.747s.544 4.603 2.747 5.303c2.134.677 4.453-.469 5.232-2.543l.07-.204c.996-3.135 4.4-4.898 7.535-3.903"
        />
        <path
            fill="#DBECFD"
            d="M39.997 34.444c1.008-3.177-.769-6.54-3.903-7.535l-.966-.259c3.135.996 4.912 4.358 3.903 7.535z"
        />
        <path
            fill="#DBECFD"
            d="M39.996 34.443c-.995 3.135-4.4 4.899-7.534 3.903l-.441-.118a6 6 0 0 1-.525-.14l.525.14c2.98.654 6.07-1.083 7.01-4.043zM35.556 28.604c2.202.699 3.445 3.1 2.746 5.302l-.966-.26c.7-2.202-.544-4.602-2.746-5.301z"
        />
        <path
            fill="#DBECFD"
            d="M38.302 33.905c-.7 2.203-3.1 3.447-5.302 2.747l-.598-.16a4 4 0 0 1-.368-.098l.368.098c2.096.46 4.273-.765 4.934-2.845z"
        />
        <path
            fill="#DBECFD"
            d="M33 36.653a4.26 4.26 0 0 1-2.058-1.447l-.966-.259a4.26 4.26 0 0 0 2.059 1.447zM29.523 36.294c.716.974 1.753 1.676 2.94 2.052l-.967-.259c-1.186-.376-2.223-1.078-2.94-2.052zM35.188 28.505q.184.04.367.098zc-2.096-.46-4.274.765-4.935 2.845l-.966-.259c.7-2.203 3.1-3.446 5.303-2.747z"
        />
        <path
            fill="#DBECFD"
            d="M30.941 35.206a.93.93 0 0 0-.484-.312l-.965-.26c.186.05.358.157.483.313zM35.569 26.768c-2.981-.654-6.07 1.084-7.01 4.044l-.965-.259c.995-3.135 4.4-4.898 7.534-3.903zq.264.059.526.14z"
        />
        <path
            fill="#DBECFD"
            d="M30.457 34.894a.87.87 0 0 0-.768.147l-.966-.259a.87.87 0 0 1 .768-.147z"
        />
        <path
            fill="#DBECFD"
            d="M29.688 35.04c-.375.3-.466.879-.165 1.254l-.966-.259c-.3-.375-.21-.953.165-1.254zM28.56 30.813l-.072.204-.965-.26.07-.203zM29.037 26.954a.91.91 0 0 0 .123-1.267l-.966-.258c.3.375.266.923-.123 1.266z"
        />
        <path
            fill="#DBECFD"
            d="M28.236 27.124c.274.073.571.015.802-.17l-.966-.259a.9.9 0 0 1-.802.17zM30.253 31.35c-.995 3.134-4.4 4.898-7.534 3.903l-.44-.118a6 6 0 0 1-.526-.141l.525.14c2.981.655 6.07-1.083 7.01-4.043z"
        />
        <path
            fill="#DBECFD"
            d="M27.772 26.831c.131.15.293.246.464.292l-.965-.259a.95.95 0 0 1-.465-.292zM28.489 31.016c-.78 2.074-3.098 3.22-5.232 2.543l-.598-.16a4 4 0 0 1-.368-.1l.368.1c2.026.444 4.13-.686 4.864-2.642zM29.16 25.687a6 6 0 0 0-2.81-1.872l-.965-.258a6 6 0 0 1 2.81 1.871z"
        />
        <path
            fill="#DBECFD"
            d="M25.813 25.51a4.1 4.1 0 0 1 1.958 1.321l-.966-.258a4.1 4.1 0 0 0-1.958-1.322z"
        />
        <path
            fill="#DBECFD"
            d="M25.445 25.41q.183.041.367.1zc-2.096-.46-4.274.766-4.935 2.846l-.966-.259c.7-2.202 3.1-3.446 5.302-2.746zM23.257 33.56c-2.202-.7-3.446-3.1-2.747-5.303l-.965-.259c-.7 2.203.544 4.603 2.746 5.302z"
        />
        <path
            fill="#DBECFD"
            d="M25.826 23.675c-2.981-.654-6.07 1.084-7.01 4.044l-.965-.259c.995-3.134 4.4-4.898 7.534-3.903zq.265.059.526.141zM18.816 27.719c-.995 3.134.768 6.539 3.903 7.534l-.966-.259c-3.134-.995-4.898-4.4-3.903-7.534z"
        />
        <path
            fill="#fff"
            d="M36.094 26.91c3.134.995 4.912 4.357 3.903 7.534-.995 3.134-4.4 4.898-7.535 3.903-1.186-.377-2.222-1.079-2.94-2.053-.3-.375-.209-.953.166-1.253.376-.3.953-.21 1.253.165a4.26 4.26 0 0 0 2.06 1.447c2.202.699 4.602-.545 5.3-2.748.7-2.202-.542-4.602-2.745-5.302s-4.603.544-5.302 2.747c-.996 3.135-4.4 4.898-7.535 3.903s-4.898-4.4-3.903-7.534c.995-3.135 4.4-4.899 7.535-3.903a6 6 0 0 1 2.809 1.871c.3.375.266.924-.122 1.267a.91.91 0 0 1-1.267-.123 4.1 4.1 0 0 0-1.958-1.321c-2.203-.7-4.603.544-5.303 2.746s.544 4.604 2.747 5.303c2.134.678 4.453-.468 5.232-2.543l.07-.204c.996-3.135 4.4-4.898 7.535-3.903"
        />
        <defs>
            <linearGradient
                id="aiTools2_svg__a"
                x1={15.282}
                x2={47.343}
                y1={31.651}
                y2={15.728}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4A8FFF" />
                <stop offset={1} stopColor="#D1E8FF" />
            </linearGradient>
            <linearGradient
                id="aiTools2_svg__b"
                x1={20.616}
                x2={28.214}
                y1={8.165}
                y2={21.108}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4785FD" />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="aiTools2_svg__e"
                x1={8.172}
                x2={9.747}
                y1={13.613}
                y2={21.962}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#C2D7F4" stopOpacity={0.4} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <clipPath id="aiTools2_svg__c" transform="translate(-4 -10)">
                <path d="M6 20c0-2.8 0-4.2.545-5.27a5 5 0 0 1 2.185-2.185C9.8 12 11.2 12 14 12h9.02c.842 0 1.263 0 1.659.064a5 5 0 0 1 2.675 1.337c.288.279.54.615 1.046 1.289.49.653.734.98 1.014 1.251A5 5 0 0 0 32 17.276c.383.07.791.08 1.607.102l9.598.246c2.735.07 4.103.105 5.145.66a5 5 0 0 1 2.122 2.177c.528 1.056.528 2.424.528 5.16V40c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185C47.2 48 45.8 48 43 48H14c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C6 44.2 6 42.8 6 40z" />
            </clipPath>
            <filter
                id="aiTools2_svg__d"
                width={49}
                height={40}
                x={4}
                y={10}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.898039 0 0 0 0 0.984314 0 0 0 1 0" />
                <feBlend in2="shape" result="effect1_innerShadow_1460_418" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1} />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0" />
                <feBlend
                    in2="effect1_innerShadow_1460_418"
                    result="effect2_innerShadow_1460_418"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={0.5} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
                <feBlend
                    in2="effect2_innerShadow_1460_418"
                    result="effect3_innerShadow_1460_418"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgAiTools2;
