import type { SVGProps } from "react";
const SvgAiTools3 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 56 56"
        {...props}
    >
        <rect
            width={29.8}
            height={29.8}
            x={21.1}
            y={4.1}
            fill="url(#aiTools3_svg__a)"
            stroke="url(#aiTools3_svg__b)"
            strokeWidth={0.2}
            rx={3.9}
        />
        <foreignObject width={40} height={40} x={3} y={8}>
            <div
                style={{
                    backdropFilter: "blur(1px)",
                    clipPath: "url(#aiTools3_svg__c)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={2} filter="url(#aiTools3_svg__d)">
            <rect
                width={36}
                height={36}
                x={5}
                y={10}
                fill="#DDF0FF"
                fillOpacity={0.6}
                rx={5}
            />
            <rect
                width={35.7}
                height={35.7}
                x={5.15}
                y={10.15}
                stroke="url(#aiTools3_svg__e)"
                strokeWidth={0.3}
                rx={4.85}
            />
        </g>
        <path
            fill="#fff"
            d="M18 19v3h-4v2h4v3h3v-8zm5 5h9v-2h-9zm5 5v3h4v2h-4v3h-3v-8zm-5 5h-9v-2h9z"
        />
        <defs>
            <linearGradient
                id="aiTools3_svg__a"
                x1={24.333}
                x2={54.983}
                y1={26}
                y2={8.546}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4A8FFF" />
                <stop offset={1} stopColor="#D1E8FF" />
            </linearGradient>
            <linearGradient
                id="aiTools3_svg__b"
                x1={29.75}
                x2={36.003}
                y1={5.2}
                y2={17.412}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4785FD" />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="aiTools3_svg__e"
                x1={23}
                x2={23}
                y1={10}
                y2={46}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#BDD6FF" />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <clipPath id="aiTools3_svg__c" transform="translate(-3 -8)">
                <rect width={36} height={36} x={5} y={10} rx={5} />
            </clipPath>
            <filter
                id="aiTools3_svg__d"
                width={40}
                height={40}
                x={3}
                y={8}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.898039 0 0 0 0 0.984314 0 0 0 1 0" />
                <feBlend in2="shape" result="effect1_innerShadow_1460_499" />
            </filter>
        </defs>
    </svg>
);
export default SvgAiTools3;
