import OperationZoneBg from '@/assets/mcp/operationZoneBg.jpg';
import DevelopZoneBg from '@/assets/mcp/developZoneBg.jpg';
import TestZoneBg from '@/assets/mcp/testZoneBg.jpg';
import {DEVELOP_ZONE_ID, OPERATION_ZONE_ID, TEST_ZONE_ID} from './constant';

const LogoMap: {[key: string]: string} = {
    [DEVELOP_ZONE_ID]: DevelopZoneBg,
    [OPERATION_ZONE_ID]: OperationZoneBg,
    [TEST_ZONE_ID]: TestZoneBg,
};

export const HeaderLeftLogo = ({zoneId}: {zoneId?: string}) => {
    const src = LogoMap[zoneId] ?? '';
    return (
        <div
            style={{
                position: 'relative',
                borderRadius: '4px',
                height: '185px',
                overflow: 'hidden',
                flex: 3,
                marginRight: '30px',
                maxWidth: '1000px',
                minWidth: '600px',
            }}
        >
            <img
                style={{position: 'absolute', left: '0', top: '0'}}
                src={src}
            />
        </div>
    );
};
