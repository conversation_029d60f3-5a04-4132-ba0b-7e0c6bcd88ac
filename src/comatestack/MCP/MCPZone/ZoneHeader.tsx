import {Flex} from 'antd';
import {useMemo, useRef} from 'react';
import styled from '@emotion/styled';
import {MCPAnnouncement} from '@/components/MCP/MCPAnnouncement';
import {useAnnouncementControl} from '@/components/MCP/MCPAnnouncement/useAnnouncementControl';
import {HeaderNavigator} from './HeaderNavigator';
import {HeaderBackground} from './HeaderBackground';
import {HeaderLeftLogo} from './HeaderLeftLogo';
import {HeaderRightContent} from './HeaderRightContent';
import {useHeaderScrollBg} from './hooks/useHeaderScrollBg';
import {ZoneTabs} from './ZoneTabs';
import {useZoneContext} from './ZoneProvider';

const Container = styled(Flex)`
    position: relative;
    padding: 0 32px;
`;

const EmptyEle = styled.div`
    position: absolute;
    top: 0px;
    left: 0px;
    width: 0px;
    height: 0px;
`;

const AnnouncementContainer = styled.div`
    padding: 0 32px;
`;

const BASE_MAX_SCROLL_TOP = 280;
const TAB_HEIGHT = 32 + 16;
const ANNOUNCEMENT_HEIGHT = 40 + 16;

export const ZoneHeader = () => {
    const {selectedZone, zoneTabItems, setSelectedZone, parentZoneId} = useZoneContext();
    const announcement = selectedZone?.announcement;
    const selectedZoneId = selectedZone?.id;
    const {closeAnnouncement, visible} = useAnnouncementControl(selectedZoneId);
    const ref = useRef<HTMLDivElement | null>(null);
    const moreTab = zoneTabItems.length > 1;
    const showAnnouncement = visible && announcement;
    const MAX_HEIGHT = useMemo(
        () => {
            let baseHeight = BASE_MAX_SCROLL_TOP;
            if (moreTab) {
                baseHeight = baseHeight + TAB_HEIGHT;
            }
            if (showAnnouncement) {
                baseHeight = baseHeight + ANNOUNCEMENT_HEIGHT;
            }
            return baseHeight;
        },
        [moreTab, showAnnouncement]
    );
    const headerCss = useHeaderScrollBg(MAX_HEIGHT);
    return (
        <>
            <EmptyEle ref={ref} />
            <HeaderNavigator zoneName={selectedZone?.name} className={headerCss} />
            <HeaderBackground zoneId={parentZoneId} height={MAX_HEIGHT} />
            {/* 这里必须要加relative, 不然会有层级问题 */}
            <Flex gap="16px" vertical style={{position: 'relative'}}>
                {
                    zoneTabItems.length > 1
                        && <ZoneTabs
                            zones={zoneTabItems}
                            selectedZone={selectedZone?.id}
                            onChange={setSelectedZone}
                        />
                }
                <Container ref={ref}>
                    <HeaderLeftLogo zoneId={parentZoneId} />
                    <HeaderRightContent zone={selectedZone} />
                </Container>
                {
                    announcement && (
                        <AnnouncementContainer>
                            <MCPAnnouncement
                                closeAnnouncement={closeAnnouncement}
                                visible={visible}
                                text={announcement}
                            />
                        </AnnouncementContainer>
                    )
                }
            </Flex>
        </>
    );
};
