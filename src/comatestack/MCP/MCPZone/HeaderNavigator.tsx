import {SettingOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {Button} from '@panda-design/components';
import {ZoneHeaderBreadcrumb} from './ZoneHeaderBreadcrumb';

const StyledHeader = styled(Flex)`
    position:sticky;
    top: 0;
    z-index: 4;
    height: fit-content;
    padding: 20px 32px;
`;

interface Props {
    zoneName: string;
    className?: string;
}

export const HeaderNavigator = ({zoneName, className}: Props) => {
    return (
        <StyledHeader justify="space-between" align="center" className={className}>
            <ZoneHeaderBreadcrumb>{zoneName}</ZoneHeaderBreadcrumb>
            {/* 管理占位 */}
            <Button style={{display: 'none'}} color="default" variant="link" icon={<SettingOutlined />} href="">
                管理
            </Button>
        </StyledHeader>
    );
};
