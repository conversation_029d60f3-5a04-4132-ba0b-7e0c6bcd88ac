import {useCallback, useMemo} from 'react';
import {useParams, useSearchParams} from 'react-router-dom';

export function useMCPZoneIdFromUrl(): {
  zoneId?: string;
  childZoneId?: string;
  updateChildZone: (zoneId: string) => void;
  } {
    const {zoneId} = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const childZoneId = searchParams.get('childZone');
    const updateChildZone = useCallback(
        (zoneId: string) => {
            setSearchParams({childZone: zoneId});
        },
        [setSearchParams]
    );
    const data = useMemo(
        () => {
            return {
                zoneId,
                childZoneId,
                updateChildZone,
            };
        },
        [childZoneId, updateChildZone, zoneId]
    );
    return data;
}
