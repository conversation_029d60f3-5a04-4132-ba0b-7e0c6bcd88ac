import {css, cx} from '@emotion/css';
import {useDebouncedCallback} from 'huse';
import {useCallback, useEffect, useMemo, useState} from 'react';

const glassEffectCss = css`
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
`;

const pureBackgroundCss = css`
    background: #fff;
`;

const emptyCss = css`
  transition: background 100ms ease-in 0s;
`;

export const useHeaderScrollBg = (MAX_SCROLL_TOP: number) => {
    const [glass, setGlass] = useState<-1|0|1>(-1);
    const calculate = useCallback(
        () => {
            const scrollTop = document.getElementById('scrollableDiv').scrollTop;
            if (scrollTop === 0) {
                setGlass(-1);
                return;
            }
            if (scrollTop > 0 && scrollTop < (MAX_SCROLL_TOP - 2)) {
                setGlass(0);
                return;
            }
            if (scrollTop >= (MAX_SCROLL_TOP - 2)) {
                setGlass(1);
            }
        },
        [MAX_SCROLL_TOP, setGlass]
    );
    const className = useMemo(
        () => {
            switch (glass) {
                case -1:
                    return emptyCss;
                case 0:
                    return cx(glassEffectCss, emptyCss);
                case 1:
                    return cx(pureBackgroundCss, emptyCss);
            }
        },
        [glass]
    );
    const calculateWithDebounce = useDebouncedCallback(calculate, 10);
    useEffect(
        () => {
            const dom = document.getElementById('scrollableDiv');
            dom.addEventListener('scroll', calculateWithDebounce);
            return () => {
                dom.removeEventListener('scroll', calculateWithDebounce);
            };
        },
        [calculateWithDebounce]
    );
    return className;
};
