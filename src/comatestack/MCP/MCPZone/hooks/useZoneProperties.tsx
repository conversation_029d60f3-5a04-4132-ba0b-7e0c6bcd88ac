import {useState, useEffect} from 'react';
import {MCPZoneDetail} from '@/api/mcp';
import {ZoneTabItem, formateZoneForTabs} from '../ZoneTabs';

export const useZoneProperties = (zone?: MCPZoneDetail): ZoneTabItem[] => {
    const [formated, setFormated] = useState([]);
    useEffect(
        () => {
            if (zone) {
                setFormated(formateZoneForTabs(zone));
            }
        },
        [zone]
    );
    return formated;
};
