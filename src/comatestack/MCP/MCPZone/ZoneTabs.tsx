import {useMemo} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {MCPZoneDetail} from '@/api/mcp';

export interface ZoneTabItem extends Omit<MCPZoneDetail, 'childZones'>{
    label: string;
}


const extractZoneBaseProperties = (zone: MCPZoneDetail) => {
    return {
        'id': zone.id,
        'name': zone.name,
        'type': zone.type,
        'description': zone.description,
        'announcement': zone.announcement,
        'publisher': zone.publisher,
        'department': zone.department,
        'icon': zone.icon,
        'serverCount': zone.serverCount,
        'subCount': zone.subCount,
        'commentCount': zone.commentCount,
    };
};

export function formateZoneForTabs(zone: MCPZoneDetail): ZoneTabItem[] {
    if (zone.childZones.length > 0) {
        const childZones = [{
            ...extractZoneBaseProperties(zone),
            label: '全部',

        }];
        zone.childZones.forEach(child => {
            childZones.push({
                ...extractZoneBaseProperties(child),
                label: child.name,
            });
        });
        return childZones;
    } else {
        return [
            {
                ...extractZoneBaseProperties(zone),
                label: zone.name,
            },
        ];
    }
}

interface Props {
  zones: ZoneTabItem[];
  selectedZone: string;
  onChange: (zoneId: string) => void;
}

const Tab = styled.div<{selected: boolean}>`
    padding: 5px 16px;
    border-radius: 32px;
    font-size: 14px;
    background-color: ${props => (props.selected ? '#E5F2FF' : '#fff')};
    color: ${props => (props.selected ? '#0080FF' : '#000')};
    cursor: pointer;
`;

const Container = styled(Flex)`
    position: relative;
    padding: 0 32px;
`;

export const ZoneTabs = ({zones, selectedZone, onChange}: Props) => {
    const tabItems = useMemo(
        () => {
            return zones.map(zone => ({
                label: zone.label,
                key: zone.id,
                selected: zone.id === selectedZone,
            }));
        },
        [zones, selectedZone]
    );
    return (
        <Container gap="4px">
            {
                tabItems.map(item => (
                    <Tab key={item.key} selected={item.selected} onClick={() => onChange(item.key)}>{item.label}</Tab>
                ))
            }
        </Container>
    );
};
