import styled from '@emotion/styled';
import {css, cx} from '@emotion/css';
import MCPServerFilter from '@/components/MCP/MCPServerFilter';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import {ZoneHeader} from './ZoneHeader';
import {ZoneProviderMixed} from './ZoneProvider';
import {initialFilterFormValue, initialTabFormValue} from './constant';

const Container = styled.div`
    position: relative;
    width: 100%;
    height: calc(100vh - 48px);
    overflow: auto;
`;

const paddingCss = css`
    padding: 0 20px 16px;
`;

const serverFilterCss = css`
    margin-top: 16px !important;
    position: sticky;
    top: 56px;
    z-index: 3;
    background-color: #fff;
    padding-bottom: 16px;
`;

const MCPZone = () => {
    return (
        <Container id="scrollableDiv">
            <ZoneHeader />
            <MCPServerFilter
                className={cx(serverFilterCss, paddingCss)}
                initialFilterFormValue={initialFilterFormValue}
                initialTabFormValue={initialTabFormValue}
            />
            <div className={paddingCss}>
                <MCPServerList scrollableTarget="scrollableDiv" />
            </div>
        </Container>
    );
};

export default () => {
    return (
        <ZoneProviderMixed>
            <MCPZone />
        </ZoneProviderMixed>
    );
};
