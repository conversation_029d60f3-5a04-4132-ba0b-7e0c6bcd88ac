import {FilterValues} from '@/components/MCP/MCPServerFilter';
import {ServerType, ServerProtocol, Order} from '@/components/MCP/MCPServerFilter/constant';
import developZoneLeftBg from '@/assets/mcp/developZoneLeftBg.png';
import developZoneRightBg from '@/assets/mcp/developZoneRightBg.png';
import operationZoneLeftBg from '@/assets/mcp/operationZoneLeftBg.png';
import operationZoneRightBg from '@/assets/mcp/operationZoneRightBg.png';
import testZoneRightBg from '@/assets/mcp/testZoneRightBg.png';
import testZoneLeftBg from '@/assets/mcp/testZoneLeftBg.png';

export const DEVELOP_ZONE_ID = '1';
export const TEST_ZONE_ID = '2';
export const OPERATION_ZONE_ID = '3';

export const initialTabFormValue = {tab: 'all'};
export const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
export const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
    platformType: 'zone',
};

export const LeftBg: Record<string, string> = {
    [TEST_ZONE_ID]: testZoneLeftBg,
    [OPERATION_ZONE_ID]: operationZoneLeftBg,
    [DEVELOP_ZONE_ID]: developZoneLeftBg,
};

export const RightBg: Record<string, string> = {
    [TEST_ZONE_ID]: testZoneRightBg,
    [OPERATION_ZONE_ID]: operationZoneRightBg,
    [DEVELOP_ZONE_ID]: developZoneRightBg,
};
