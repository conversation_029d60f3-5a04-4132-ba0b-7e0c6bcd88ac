import {Flex, Typography, Divider} from 'antd';
import {ReactNode} from 'react';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import {IconMcp, IconSubscribe2} from '@/icons/mcp';
import {ZoneTabItem} from './ZoneTabs';

interface StatisticsProps {
  label: string;
  icon: ReactNode;
  number: string | number;
  splitLine?: boolean;
}

const ZoneStatistics = ({label, icon, number, splitLine}: StatisticsProps) => {
    return (
        <Flex align="center">
            <Flex>
                <span>{icon}</span>
                <span style={{color: '#5C5C5C', marginLeft: '4px'}}>{label}</span>
            </Flex>
            <span style={{marginLeft: '8px', fontSize: '14px', fontWeight: 400}}>{number}</span>
            {
                splitLine && <Divider type="vertical" style={{margin: '0 32px', borderColor: '#CCCCCB'}} />
            }
        </Flex>
    );
};

interface PropertyProps{
  name: string;
  value: ReactNode;
}

const HeaderProperty = ({name, value}: PropertyProps) => {
    return (
        <Flex>
            <Typography.Text style={{color: '#5C5C5C'}}>{name}</Typography.Text>
            <div style={{marginLeft: '24px'}}>
                {value}
            </div>
        </Flex>
    );
};

interface Props {
  zone?: ZoneTabItem;
}

export const HeaderRightContent = ({zone}: Props) => {
    return (
        <Flex
            vertical
            gap="16px"
            style={{flexBasis: '550px', marginLeft: '20px', flexGrow: 1, flexShrink: 0}}
        >
            <Typography.Text
                style={{fontSize: '16px', lineHeight: '24px'}}
            >
                {zone?.description}
            </Typography.Text>
            <Divider style={{margin: '0'}} />
            <HeaderProperty name="出品人" value={zone?.department} />
            <HeaderProperty name="联系人" value={<UserAvatarList users={zone?.publisher} max={2} />} />
            <Flex>
                <ZoneStatistics
                    label="MCP数"
                    icon={<IconMcp />}
                    number={zone?.serverCount}
                    splitLine
                />
                <ZoneStatistics
                    label="订阅量"
                    icon={<IconSubscribe2 />}
                    number={zone?.subCount}
                />
            </Flex>
        </Flex>
    );
};
