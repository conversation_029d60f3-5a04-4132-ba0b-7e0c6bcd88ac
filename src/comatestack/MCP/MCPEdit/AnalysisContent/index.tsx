/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable max-len */
import {useEffect, useState, useCallback} from 'react';
import {Flex, Table, Tag, Button} from 'antd';
import {ColumnsType} from 'antd/es/table';
import styled from '@emotion/styled';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import {apiGetMCPServerData, apiGetToolLogList, MCPServerDataResponse, ToolLogItem} from '@/api/mcp';
import {formatISOTime} from '@/utils/date';

const SectionWrapper = styled.div`
    margin-bottom: 16px;
    border: 1px solid var(--Tokens-disabled, #D9D9D9);
    border-radius: 8px;
    padding: 20px;
`;

const SectionTitle = styled.h4`
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #000;
`;

const MetricsCardContainer = styled(Flex)`
    gap: 12px;
`;

const MetricCard = styled.div`
    flex: 1;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
`;

const MetricLabel = styled.div`
    color: #545454;
    font-size: 14px;
    margin-bottom: 8px;
`;

const MetricValue = styled.div`
    font-size: 24px;
    font-weight: bold;
    color: #000;
`;

interface ToolData {
    id: number;
    name: string;
    callCount: number;
    callSuccessRate: number;
}

const AnalysisContent = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const [serverData, setServerData] = useState<MCPServerDataResponse | null>(null);
    const [toolLogList, setToolLogList] = useState<ToolLogItem[]>([]);
    const [toolLogLoading, setToolLogLoading] = useState(false);
    const [toolLogPagination, setToolLogPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [sortOrder, setSortOrder] = useState<{
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    }>({});

    const loadServerData = useCallback(
        async () => {
            if (!mcpServerId) {return;}
            try {
                const data = await apiGetMCPServerData({mcpServerId});
                setServerData(data);
            } catch (error) {
                console.error('Failed to load server data:', error);
            }
        },
        [mcpServerId]
    );

    const loadToolLogList = useCallback(
        async (params?: {
        pn?: number;
        size?: number;
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    }) => {
            if (!mcpServer?.serverKey) {
                return;
            }

            setToolLogLoading(true);
            try {
                const response = await apiGetToolLogList({
                    serverKey: mcpServer.serverKey,
                    pn: params?.pn || toolLogPagination.current,
                    size: params?.size || toolLogPagination.pageSize,
                    ...params,
                });
                setToolLogList(response.data || []);
                setToolLogPagination(prev => ({
                    ...prev,
                    current: params?.pn || prev.current,
                    total: response.totalCount || 0,
                }));
            } catch (error) {
                console.error('Failed to load tool log list:', error);
            } finally {
                setToolLogLoading(false);
            }
        },
        [mcpServer.serverKey, toolLogPagination]
    );

    useEffect(
        () => {
            loadServerData();
        },
        [loadServerData]
    );

    useEffect(
        () => {
            if (mcpServer?.serverKey) {
                loadToolLogList();
            }
        },
        [mcpServer?.serverKey]
    );

    const toolDetailColumns: ColumnsType<ToolData> = [
        {
            title: '工具名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '调用数量',
            dataIndex: 'callCount',
            key: 'callCount',
        },
        {
            title: '调用成功率',
            dataIndex: 'callSuccessRate',
            key: 'callSuccessRate',
            render: (rate: number) => `${rate}%`,
        },
        {
            title: '调用来源',
            key: 'source',
            render: () => '详情',
        },
    ];

    const toolLogColumns: ColumnsType<ToolLogItem> = [
        {
            title: '调用ID',
            dataIndex: 'id',
            key: 'id',
            sorter: true,
            sortOrder: sortOrder.idOrder === 'DESC' ? 'descend' : sortOrder.idOrder === 'ASC' ? 'ascend' : null,
        },
        {
            title: '时间',
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            sortOrder: sortOrder.createTimeOrder === 'DESC' ? 'descend' : sortOrder.createTimeOrder === 'ASC' ? 'ascend' : null,
            render: (time: string) => formatISOTime(time),
        },
        {
            title: '耗时',
            dataIndex: 'duration',
            key: 'duration',
            sorter: true,
            sortOrder: sortOrder.durationTimeOrder === 'DESC' ? 'descend' : sortOrder.durationTimeOrder === 'ASC' ? 'ascend' : null,
            render: (duration: number) => `${duration}ms`,
        },
        {
            title: '工具',
            dataIndex: 'toolName',
            key: 'toolName',
        },
        {
            title: '状态',
            dataIndex: 'success',
            key: 'success',
            render: (success: boolean) => (
                <Tag color={success ? 'success' : 'error'}>
                    {success ? '成功' : '失败'}
                </Tag>
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: () => (
                <Button type="link" size="small">
                    查看详情
                </Button>
            ),
        },
    ];

    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        const newSortOrder: typeof sortOrder = {};

        if (sorter.field === 'id') {
            newSortOrder.idOrder = sorter.order === 'descend' ? 'DESC' : sorter.order === 'ascend' ? 'ASC' : undefined;
        } else if (sorter.field === 'createTime') {
            newSortOrder.createTimeOrder = sorter.order === 'descend' ? 'DESC' : sorter.order === 'ascend' ? 'ASC' : undefined;
        } else if (sorter.field === 'duration') {
            newSortOrder.durationTimeOrder = sorter.order === 'descend' ? 'DESC' : sorter.order === 'ascend' ? 'ASC' : undefined;
        }

        setSortOrder(newSortOrder);
        loadToolLogList({
            pn: pagination.current,
            size: pagination.pageSize,
            ...newSortOrder,
        });
    };

    const handlePaginationChange = (page: number, pageSize: number) => {
        setToolLogPagination(prev => ({
            ...prev,
            current: page,
            pageSize,
        }));
        loadToolLogList({
            pn: page,
            size: pageSize,
            ...sortOrder,
        });
    };

    return (
        <div>
            <SectionWrapper>
                <SectionTitle>MCP Server核心指标</SectionTitle>
                <MetricsCardContainer>
                    <MetricCard>
                        <MetricLabel>数据浏览量</MetricLabel>
                        <MetricValue>{serverData?.pv || 0}/{serverData?.uv || 0}</MetricValue>
                    </MetricCard>
                    <MetricCard>
                        <MetricLabel>总调用量</MetricLabel>
                        <MetricValue>{serverData?.callCount || 0}</MetricValue>
                    </MetricCard>
                    <MetricCard>
                        <MetricLabel>订阅应用量</MetricLabel>
                        <MetricValue>{serverData?.subCount || 0}</MetricValue>
                    </MetricCard>
                    <MetricCard>
                        <MetricLabel>Server Config复制量</MetricLabel>
                        <MetricValue>{serverData?.copyCount || 0}</MetricValue>
                    </MetricCard>
                    <MetricCard>
                        <MetricLabel>总调用成功率</MetricLabel>
                        <MetricValue>{serverData?.callSuccessRate || 0}%</MetricValue>
                    </MetricCard>
                </MetricsCardContainer>
            </SectionWrapper>

            <SectionWrapper>
                <SectionTitle>工具详情</SectionTitle>
                <Table
                    columns={toolDetailColumns}
                    dataSource={serverData?.toolDataList || []}
                    rowKey="id"
                    pagination={false}
                />
            </SectionWrapper>

            <SectionWrapper>
                <SectionTitle>调用历史</SectionTitle>
                <Table
                    columns={toolLogColumns}
                    dataSource={toolLogList}
                    rowKey="id"
                    loading={toolLogLoading}
                    pagination={{
                        current: toolLogPagination.current,
                        pageSize: toolLogPagination.pageSize,
                        total: toolLogPagination.total,
                        onChange: handlePaginationChange,
                        showSizeChanger: true,
                        showQuickJumper: true,
                    }}
                    onChange={handleTableChange}
                />
            </SectionWrapper>
        </div>
    );
};

export default AnalysisContent;
