import {Form, Flex, Radio, Select, Checkbox} from 'antd';
import {useRequest} from 'huse';
import {EmailGroupSelect} from '@/components/MCP/EmailGroupSelect';
import {apiGetAllZones} from '@/api/mcp';
import {Channel2Type, CHANNEL2_OPTIONS, VisibilityType} from './types';

const getAllZones = () => apiGetAllZones().then(res => {
    const flatten: Array<{label: string, value: string}> = [];
    // 只有两层，无需递归
    res.forEach(zone => {
        flatten.push({label: zone.name, value: zone.id});
        zone.childZones.forEach(child => {
            flatten.push({label: child.name, value: child.id});
        });
    });
    return flatten;
});

export const Range = () => {
    const {data: zones} = useRequest(getAllZones, {});
    return (
        <>
            <Form.Item
                labelCol={{span: 4}}
                label="专区"
                name="zoneId"
            >
                <Select options={zones} allowClear placeholder="请根据MCP的适用场景选择专区" />
            </Form.Item>
            <Form.Item
                labelCol={{span: 4}}
                label="发布渠道"
                name="channelType"
            >
                <Checkbox.Group options={CHANNEL2_OPTIONS} />
            </Form.Item>
            <Form.Item label="发布范围" labelCol={{span: 4}} required>
                <Flex vertical gap={8}>
                    <Form.Item noStyle dependencies={['channelType']}>
                        {
                            ({getFieldValue}) => {
                                const shouldPublic = getFieldValue('channelType')?.[0] === Channel2Type.AI;
                                return (
                                    <Form.Item name="visibilityType" style={{marginBottom: 0}}>
                                        <Radio.Group>
                                            <Radio value={VisibilityType.PUBLIC}>公开</Radio>
                                            <Radio value={VisibilityType.RANGE} disabled={shouldPublic}>
                                                指定范围
                                            </Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                );
                            }
                        }
                    </Form.Item>
                    {/* 使用dependencies有时会漏掉一些更新，故使用shouldUpdate */}
                    <Form.Item noStyle shouldUpdate={(pre, cur) => pre.visibilityType !== cur.visibilityType}>
                        {({getFieldValue}) => {
                            const visibilityTypeValue = getFieldValue('visibilityType');
                            return visibilityTypeValue === VisibilityType.RANGE ? (
                                <Form.Item
                                    name="rangeContent"
                                    rules={[{
                                        required: true,
                                        message: '请选择邮箱或邮件组',
                                    }]}
                                >
                                    <EmailGroupSelect widthUser />
                                </Form.Item>
                            ) : null;
                        }}
                    </Form.Item>
                </Flex>
            </Form.Item>
        </>
    );
};
