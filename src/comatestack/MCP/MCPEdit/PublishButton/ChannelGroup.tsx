import {Checkbox} from 'antd';
import {CHANNEL_OPTIONS} from './types';

interface ChannelGroupProps {
    value?: string[];
    onChange?: (value: string[]) => void;
}

export const ChannelGroup = ({value, onChange}: ChannelGroupProps) => {
    return (
        <>
            <Checkbox checked disabled>空间内</Checkbox>
            <Checkbox.Group options={CHANNEL_OPTIONS} value={value} onChange={onChange} />
        </>
    );
};
