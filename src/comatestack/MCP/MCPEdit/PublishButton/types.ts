import {PublishInfo} from '@/api/mcp';

export enum PublishType {
  HUB = 'hub',
  WORKSPACE = 'workspace'
}

export enum VisibilityType {
  PUBLIC = 'PUBLIC',
  RANGE = 'RANGE',
}

export enum ChannelType {
  SPACE = 'space',
  HUB = 'hub',
}

export enum Channel2Type {
  ZULU = 'COMATE',
  AI = 'AI_CENTER',
}

export interface FormValues extends Omit<PublishInfo, 'rangeContent'|'publishType'|'channelType'> {
  rangeContent?: string[];
  channelType?: string[];
  channel: string[];
}

export const getInitialValues = (values?: PublishInfo): FormValues => {
    if (values) {
        return {
            channel: values.publishType === ChannelType.HUB ? [ChannelType.HUB] : [],
            visibilityType: values.visibilityType,
            contacts: values.contacts,
            rangeContent: values.visibilityType === VisibilityType.RANGE ? values.rangeContent?.content : undefined,
            zoneId: values.zoneId,
            channelType: values.channelType ? [values.channelType] : [],
        };
    }
    return {
        channel: [ChannelType.HUB],
        visibilityType: VisibilityType.PUBLIC,
        contacts: [],
    };
};

export const transformPublishParams = (values: FormValues, mcpServerId: number) => {
    return {
        mcpServerId,
        visibilityType: values.visibilityType,
        publishType: values.channel?.includes(ChannelType.HUB) ? PublishType.HUB : PublishType.WORKSPACE,
        contacts: values.contacts,
        channelType: values.channelType && values.channelType[0],
        zoneId: values.zoneId,
        ...values.visibilityType === VisibilityType.RANGE ? {
            rangeContent: {
                rangeType: 'EMAIL_GROUP',
                content: values.rangeContent,
            },
        } : {},
    };
};

export const CHANNEL_OPTIONS = [
    {label: '广场', value: ChannelType.HUB},
];

export const CHANNEL2_OPTIONS = [
    {label: '发布到Zulu MCP广场', value: Channel2Type.ZULU},
    {label: '发布到AI能力中心', value: Channel2Type.AI},
];
