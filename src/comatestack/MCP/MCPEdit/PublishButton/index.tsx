import {Button} from '@panda-design/components';
import {useBoolean} from 'huse';
import {IconSend} from '@/icons/comatestack';
import {ConfirmModal} from './ConfirmModal';

export const SendButton = () => {
    const [open, {on, off}] = useBoolean();
    return (
        <>
            <Button
                disabled={false}
                type="primary"
                onClick={on}
                icon={<IconSend />}
            >
                发布
            </Button>
            {/* 用实例的创建与销毁来控制其与生命周期相关的代码逻辑，而非使用open类的入参控制 */}
            {open && <ConfirmModal onCancel={off} />}
        </>
    );
};

export default SendButton;
