/* eslint-disable max-lines */
import {keys, set} from 'lodash';
import {Path} from '@panda-design/path-form';
import {BaseParam, Description, JSONSchema} from '@/types/mcp/mcp';

export interface TreeData {
    title: string;
    value: string;
    key: string;
    disabled?: boolean;
    bodyPath?: Path;
    children?: TreeData[];
}

const isArrayType = (type: string) => {
    return type === 'array';
};

const isObjectType = (type: string) => {
    return type === 'object';
};

const isArrayOrObjectType = (type: string) => {
    return isArrayType(type) || isObjectType(type);
};

export const getBodySchemaTreeData = (list: BaseParam[], titleWidthRoot: boolean): TreeData[] => {
    return list.map(item => {
        return {
            title: titleWidthRoot ? item?.key : item?.name,
            value: item?.key,
            key: item?.fieldPath?.join('.'),
            bodyPath: item?.fieldPath,
            // array下的字段在参数配置中只能看不能选，故快捷选择中不展开
            children: item?.children && !isArrayType(item?.type)
                ? getBodySchemaTreeData(item?.children, titleWidthRoot) : [],
        };
    });
};

// 用于提交表单时，构建请求体
export const fillParamValueToJSONProperties = (params: BaseParam[], jsonSchema: JSONSchema): JSONSchema => {
    params?.forEach(param => {
        const {value, children, fieldPath} = param;
        const endPath = fieldPath.slice(1);
        const jsonPath = [];
        endPath.forEach(p => {
            jsonPath.push('properties');
            jsonPath.push(p);
        });
        jsonPath.push('value');
        set(
            jsonSchema,
            jsonPath,
            value
        );
        if (children) {
            fillParamValueToJSONProperties(children, jsonSchema);
        }
    });
    return jsonSchema;
};

const enum CUSTOM_FIELD {
    canRelate = 'canRelate',
    fieldPath = 'fieldPath',
    key = 'key',
}

export const CUSTOM_FIELD_IN_BASE_PARAMS = [
    CUSTOM_FIELD.canRelate, CUSTOM_FIELD.fieldPath, CUSTOM_FIELD.key,
];

/**
 * 自己构建的数据结构，用于适配界面展示与表单数据联动
 */
export interface CustomBaseParams extends BaseParam {
    children?: BaseParam[];
}

function createBaseParam(
    schema: JSONSchema | Description,
    key: string,
    parentPath: string[],
    required: boolean,
    disableRelate: boolean
): BaseParam {
    const fieldPath = parentPath.length > 0 ? [...parentPath, key] : ['body', key];
    const item: CustomBaseParams = {
        ...schema,
        name: key,
        [CUSTOM_FIELD.key]: fieldPath.join('.'),
        [CUSTOM_FIELD.fieldPath]: fieldPath,
        required,
        children: isArrayOrObjectType(schema.type) ? [] : undefined,
        [CUSTOM_FIELD.canRelate]: disableRelate === true ? false : !isObjectType(schema.type),
    };
    return item;
}

// 这个函数是为了将body中的数据转换为可以在table中渲染的树形数据
export const transformBodySchemaToTableTreeData = (
    jsonSchema: JSONSchema | Description,
    options: {
        parentPath?: string[];
        parent?: any;
        // 如果参数是数组类型，则disableRelate为true，表示下级所有字段都不可关联
        disableRelate?: boolean;
    } = {}
) => {
    if (!jsonSchema) {
        return undefined;
    }
    const {parentPath = [], parent, disableRelate} = options;
    const handleParent = parent ?? [];
    if (isArrayOrObjectType(jsonSchema.type)) {
        const childMap = isObjectType(jsonSchema.type)
            ? ((jsonSchema as JSONSchema).properties ?? {}) : {items: (jsonSchema as JSONSchema).items};
        const required = isObjectType(jsonSchema.type) ? (jsonSchema as JSONSchema).required : [];
        Object.entries(childMap).forEach(([key, value]) => {
            const item = createBaseParam(value, key, parentPath, required?.includes(key), disableRelate);
            handleParent.push(item);
            if (isArrayOrObjectType(value.type)) {
                transformBodySchemaToTableTreeData(value as JSONSchema, {
                    parentPath: item.fieldPath as string[],
                    parent: item.children,
                    disableRelate: disableRelate === true ? true : isArrayType(value.type),
                });
            }
        });
        return handleParent;
    }
    // 从数据结构看，应该是不会走到这个分支的。
    console.error('处理jsonSchema的方式需调整', jsonSchema);
};

/**
 * 处理除Body外的其它请求参数。
 * @param parameters 一个对象，键是header、query、path、cookie，值是参数列表
 */
export const transformRequestParamsToTableData = (
    parameters: Record<string, Array<BaseParam & {canRelate: boolean}>>
) => {
    return keys(parameters)?.reduce(
        (acc, cur) => {
            acc[cur] = parameters[cur]?.map(param => ({
                ...param,
                key: `${cur}.${param.name}`,
                fieldPath: [`${cur}`, `${param.name}`],
                canRelate: true,
            }));
            return acc;
        },
        {} as Record<string, BaseParam[]>
    );
};

export function tapIndexAsKey<T>(list: T[], keyName: string = 'key'): Array<T & {[keyName: string]: number}> {
    return list.map((item, index) => ({
        ...item,
        [keyName]: index,
    }));
}
