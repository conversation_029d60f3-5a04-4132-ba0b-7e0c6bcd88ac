import styled from '@emotion/styled';
import {Button, message, Modal} from '@panda-design/components';
import {Form, Space} from 'antd';
import {useBoolean} from 'huse';
import {useCallback} from 'react';
import {apiPutMCPServer} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {useSpaceLabels} from '@/regions/mcp/mcpSpace';
import {IconAlert} from '@/icons/mcp';
import {MCPEditTab} from '@/types/mcp/mcp';
import {serverConfigParse} from '../MCPCreate/hooks';
import {resetTouchedBasePath} from './regions';
import SaveToolButton from './ToolsContent/SaveToolButton';
import {useFormUpdated} from './Providers/FormUpdatedProvider';
import {useToolParamsConfigContext} from './Providers/ToolParamsConfigProvider';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    border: 1px solid #317ff5 !important;
    &:hover{
        color: #317ff5 !important;
        border: 1px solid #317ff5 !important;
    }
`;
interface Props {
    activeTab: MCPEditTab;
}

const ActionButtons = ({activeTab}: Props) => {
    const mcpServerId = useMCPServerId();
    const [loading, {on, off}] = useBoolean();
    const mcpServer = useMCPServer(mcpServerId);
    const spaceLabels = useSpaceLabels(mcpServer?.workspaceId);
    const {validateFields} = Form.useFormInstance();
    const saveServerInfo = useCallback(
        async () => {
            try {
                on();
                const values = await validateFields();
                const {serverInfo} = values;
                const {serverConf, labels} = serverInfo;
                const {serverConfig, serverExtension} = serverConf ?? {};

                const {serverAuthType, ...restServerExtension} = serverExtension || {};

                await apiPutMCPServer({
                    mcpServerId,
                    ...mcpServer,
                    ...serverInfo,
                    serverAuthType,
                    labels: spaceLabels?.filter(({id}) =>
                        labels.includes(id)).map(({id, labelValue}) => ({id, labelValue})
                    ),
                    serverConf: {
                        ...mcpServer.serverConf,
                        ...serverConf,
                        serverConfig: serverConfigParse(serverConfig, serverInfo.serverProtocolType),
                        serverExtension: restServerExtension,
                    },
                });

                resetTouchedBasePath();
                await loadMCPServer({mcpServerId});

                setTimeout(() => {
                    resetTouchedBasePath();
                }, 100);

                off();
                message.success('保存成功');
            } catch (e) {
                console.error(e);
                off();
                message.warning(e.response.data?.msg || '请检查表单');
            }
        },
        [mcpServer, mcpServerId, off, on, spaceLabels, validateFields]
    );
    const saveWidthConfirm = () => {
        if (mcpServer.serverStatus === 'draft') {
            saveServerInfo();
        } else {
            Modal.confirm({
                content: '当前MCP Server已发布，点击保存该修改会立马生效，对订阅的应用产生影响，请确认是否继续保存',
                icon: <IconAlert />,
                okText: '保存',
                cancelText: '取消',
                onOk: () => {
                    saveServerInfo();
                },
            });
        }
    };

    const [, setFormUpdated] = useFormUpdated();
    const {toolParamsChanged} = useToolParamsConfigContext();
    const onSuccess = useCallback(
        () => {
            setFormUpdated(true);
            toolParamsChanged.current = false;
        },
        [toolParamsChanged, setFormUpdated]
    );

    return (
        <Space>
            {activeTab === MCPEditTab.ServerInfo && (
                <StyledButton
                    loading={loading}
                    type="text"
                    tooltip={<Space><IconAlert style={{color: '#F58300'}} />基本信息发生修改，保存后数据不会丢失～</Space>}
                    onClick={saveWidthConfirm}
                >
                    保存基本信息
                </StyledButton>
            )}
            {activeTab === MCPEditTab.Tools && (
                <SaveToolButton
                    setFormUpdated={setFormUpdated}
                    onSuccess={onSuccess}
                    mcpServer={mcpServer}
                />
            )}
        </Space>
    );
};

export default ActionButtons;

