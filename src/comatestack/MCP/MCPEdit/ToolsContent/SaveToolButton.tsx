import styled from '@emotion/styled';
import {Button, Modal} from '@panda-design/components';
import {Space} from 'antd';
import {useBoolean} from 'huse';
import {IconAlert} from '@/icons/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {useHandleSaveTool} from './hooks';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    border: 1px solid #317ff5 !important;
    &:hover{
        color: #317ff5 !important;
        border: 1px solid #317ff5 !important;
    }
`;
interface Props {
    mcpServer?: MCPServerBase;
    onSuccess: () => void;
    setFormUpdated: (updated: boolean) => void;
}
export default function SaveToolButton({onSuccess, mcpServer, setFormUpdated}: Props) {
    const [loading, {on, off}] = useBoolean();
    const handleSave = useHandleSaveTool({on, off, onSuccess});

    const saveWidthConfirm = () => {
        if (mcpServer?.serverStatus === 'release') {
            Modal.confirm({
                content: '当前MCP Server已发布，点击保存该修改会立马生效，对订阅的应用产生影响，请确认是否继续保存',
                icon: <IconAlert />,
                okText: '保存',
                cancelText: '取消',
                onOk: () => {
                    handleSave();
                    setFormUpdated(true);
                },
            });
        } else {
            handleSave();
            setFormUpdated(true);
        }
    };

    return (
        <StyledButton
            disabled={false}
            type="text"
            tooltip={<Space><IconAlert style={{color: '#F58300'}} />工具发生修改后，请及时保存～</Space>}
            onClick={saveWidthConfirm}
            loading={loading}
        >
            保存工具
        </StyledButton>
    );
}
