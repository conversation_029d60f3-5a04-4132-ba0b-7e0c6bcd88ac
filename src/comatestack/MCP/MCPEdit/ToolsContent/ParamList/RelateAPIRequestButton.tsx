import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Form} from 'antd';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {CustomTreeSelect} from '../CustomTreeSelect';
import {useActiveTool, useHandleRelateParamTreeData} from '../hooks';
import {useToolParamsConfigContext} from '../../Providers/ToolParamsConfigProvider';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const StyledCustomTreeSelect = styled(CustomTreeSelect)`
    .ant-5-select-selector {
        position: absolute !important;
        left: 0;
        z-index: -1;
    }
`;

const RelateAPIRequestButton = () => {
    const {batchSetToolParams} = useToolParamsConfigContext();
    const [open, {on, off}] = useBoolean();
    const [selectedTrees, setSelectedTrees] = useState<string[]>([]);
    const {activeToolIndex} = useActiveTool();
    const paramsPath = useMemo(
        () => {
            return ['tools', activeToolIndex, 'toolParams', 'toolParams'];
        },
        [activeToolIndex]
    );
    const params: BaseParam[] = Form.useWatch(paramsPath);
    const treeData = useHandleRelateParamTreeData({canSelectRoot: true, titleWidthRoot: false});
    const handleChange = useCallback(
        (values: string[]) => {
            batchSetToolParams(activeToolIndex, values);
        },
        [activeToolIndex, batchSetToolParams]
    );

    useEffect(
        () => {
            setSelectedTrees(params?.map(item => item?.refParam));
        },
        [params]
    );

    useEffect(
        () => {
            window.addEventListener('click', off);
        },
        [off]
    );
    return (
        <StyledButton
            icon={<IconPlus />}
            onClick={e => {
                e.stopPropagation();
                on();
            }}
            type="text"
        >
            关联API请求参数
            <StyledCustomTreeSelect
                multiple
                treeCheckable
                onChange={handleChange}
                treeData={treeData}
                open={open}
                noStyle
                value={selectedTrees}
            />
        </StyledButton>
    );
};

export default RelateAPIRequestButton;

