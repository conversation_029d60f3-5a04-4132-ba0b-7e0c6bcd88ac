/* eslint-disable max-lines */
import {Flex, Form, Splitter} from 'antd';
import {useBoolean} from 'huse';
import {createContext, ReactNode, useContext, useEffect, useState, useCallback} from 'react';
import styled from '@emotion/styled';
import {DataNode} from 'antd/es/tree';
import {loadMCPServerTools, useMCPServer, useMCPServerTools} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import {MCPToolContext} from '@/components/MCP/MCPToolDebug/Providers/MCPToolContext';
import ApiDefine from './ApiDefine';
import ToolsDefine from './ToolsDefine';
import EmptyTool from './EmptyTool';
import ToolActions from './ToolActions';
import {useActiveTool} from './hooks';
import ToolsList from './ToolList';

const ContentWrapper = styled(Flex)`
    border: 1px solid #E8E8E8;
    flex-grow: 1;
    border-radius: 6px;
    flex-direction: column;
`;

const ToolFormContent = styled(Flex)`
    flex-grow: 1;
    justify-content: center;
    padding: 0 24px;
    width: 100%;
`;

const Line = styled.div`
    height: 100%;
    width: 1px;
    background-color: #E8E8E8;
    margin: 0 16px;
`;

const Wrapper = styled.div`
    height: 100%;
    position: relative;
`;

interface ContextValue {
    treeData: DataNode[];
    setTreeData: (treeData: DataNode[]) => void;
}


const Context = createContext<ContextValue>({} as ContextValue);

export const TreeDataProvider = ({children}: {children: ReactNode}) => {
    const [treeData, setTreeData] = useState<DataNode[]>();
    return (
        <Context.Provider
            value={{
                treeData,
                setTreeData,
            }}
        >
            {children}
        </Context.Provider>
    );
};

export const useTreeDataProviderContext = () => {
    return useContext(Context);
};

const validateActiveToolIndex = (activeToolIndex: number, toolCount: number) => {
    if (activeToolIndex < 0) {
        return false;
    }
    if (activeToolIndex > toolCount - 1) {
        return false;
    }
    return true;
};


const ToolsContent = () => {
    const serverSourceType = Form.useWatch('serverSourceType');
    const tools = Form.useWatch('tools');
    const [isShowSideToolList, {on: showToolList, off: hideToolList}] = useBoolean(true);
    const [isShowApiDefine, {on: showApiDefine, off: hideApiDefine}] = useBoolean(false);
    const [splitterSize, setSplitterSize] = useState<number>(250);
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const form = Form.useFormInstance();
    const {setFieldValue} = form;
    const mcpServerTools = useMCPServerTools(mcpServerId);
    // const replace = useSearchReplace();
    const {activeToolIndex, setActiveToolIndex} = useActiveTool();
    const activeTool = Form.useWatch(['tools', activeToolIndex]);

    const handleSplitterResize = useCallback(
        (sizes: number[]) => {
            setSplitterSize(sizes[0]);
        },
        []
    );

    useEffect(
        () => {
            loadMCPServerTools({mcpServerId});
        },
        [mcpServerId]
    );
    useEffect(
        () => {
            if (mcpServerTools) {
                setFieldValue('tools', mcpServerTools);
            }
        },
        [mcpServerTools, setFieldValue]
    );
    useEffect(
        () => {
            if (tools === undefined) {
                return;
            }
            if (!validateActiveToolIndex(activeToolIndex, tools?.length ?? 0)) {
                setActiveToolIndex(0);
            }
        },
        [activeToolIndex, setActiveToolIndex, tools]
    );
    useEffect(
        () => {
            if (serverSourceType === 'openapi') {
                showApiDefine();
            }
        },
        [serverSourceType, showApiDefine]
    );
    if (mcpServerTools?.length === 0 && !tools?.length) {
        const noAction = mcpServer?.serverSourceType === 'external';
        return <EmptyTool noAction={noAction} />;
    }

    return (
        <Wrapper>
            <TreeDataProvider>
                <Splitter style={{height: '100%'}} onResize={handleSplitterResize}>
                    <Splitter.Panel
                        size={splitterSize}
                        min={200}
                        max={550}
                        resizable={isShowSideToolList}
                    >
                        <ToolsList displayAll={isShowSideToolList} on={showToolList} off={hideToolList} />
                    </Splitter.Panel>
                    <Splitter.Panel>
                        <ContentWrapper>
                            <MCPToolContext
                                mcpId={mcpServerId}
                                toolId={activeTool?.id}
                                toolKey={activeTool?.toolKey}
                                inPlayground={false}
                            >
                                <ToolActions />
                            </MCPToolContext>
                            <ToolFormContent>
                                <ToolsDefine showApiDefine={isShowApiDefine} />
                                {isShowApiDefine && <Line />}
                                {serverSourceType === 'openapi' && <ApiDefine
                                    showApiDefine={isShowApiDefine}
                                    hide={hideApiDefine}
                                    show={showApiDefine}
                                />}
                            </ToolFormContent>
                        </ContentWrapper>
                    </Splitter.Panel>
                </Splitter>
            </TreeDataProvider>
        </Wrapper>
    );
};

export default ToolsContent;
