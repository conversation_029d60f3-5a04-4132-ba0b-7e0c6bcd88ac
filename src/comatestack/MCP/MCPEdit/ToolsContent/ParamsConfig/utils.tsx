import {BaseParam} from '@/types/mcp/mcp';

export const isParamRelated = (key: string, paramsList: BaseParam[]) => {
    return paramsList?.some(
        item => item.refParam === key
    );
};

export const extractKeyPath = (key: string): string[] => {
    const keys = key.split('.');
    // 最后一项是校验的目标，不需要展开
    const path = keys.slice(0, -1).map((_, i) => {
        const pre = [];
        for (let j = 0; j < i + 1; j++) {
            pre.push(keys[j]);
        }
        return pre.join('.');
    });
    return path;
};
