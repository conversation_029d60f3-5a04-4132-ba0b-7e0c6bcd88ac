/* eslint-disable max-lines */
import {Checkbox, Flex, Form, TableColumnsType, Tabs, Typography} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {keys} from 'lodash';
import {IconDownSolid} from '@baidu/ee-icon';
import {Gap} from '@/design/iplayground/Gap';
import {BaseParam} from '@/types/mcp/mcp';
import {StyledTable} from '../ParamList';
import {ApiParamsValidationError, useActiveTool} from '../hooks';
import {useMCPEditFormValidationListener} from '../../Providers/MCPEditFormValidationInteractionProvider';
import ParamValueField from './ParamValueField';
import {ParamNameField} from './ParamNameField';
import {extractKeyPath} from './utils';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab-active{
        .ant-5-tabs-tab-btn{
            color: #0083FF !important;
        }
    }
    .ant-5-tabs-tab{
        font-size: 14px;
        &:not(:first-child) {
            margin-left: 24px;
        }
    }
`;

const StyledTriangle = styled(IconDownSolid) <{expanded?: boolean}>`
    color: #BFBFBF;
    position: absolute;
    left: 32px;
    top: ${props => (props.expanded ? 'calc(50% - 4px)' : 'calc(50% - 6px)')};
    z-index: 10;
    margin-right: 8px;
    cursor: pointer;
    transform: ${props => (props.expanded ? 'rotate(0deg)' : 'rotate(-90deg)')};
`;

// ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
const ParamsConfig = () => {
    const form = Form.useFormInstance();
    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
    const {subscribeValidationError} = useMCPEditFormValidationListener();
    const [showRequired, setShowRequired] = useState(false);
    // path、query、cookie、body等，source对象下的key
    const [activeKey, setActiveKey] = useState<string>();
    const {activeToolIndex} = useActiveTool();
    const sourcePath = useMemo(
        () => ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
        [activeToolIndex]
    );
    const source = Form.useWatch(sourcePath);
    const tabItems = useMemo(
        () => {
            const sourceKeys = keys(source);
            const items = sourceKeys.map(key => ({
                label: key,
                value: key,
                key,
            }));
            return items;
        },
        [source]
    );
    useEffect(
        () => {
            if (!activeKey) {
                setActiveKey(tabItems?.[0]?.key);
            }
        },
        [activeKey, tabItems]
    );

    const expand = useCallback(
        (keys: string[]) => {
            setExpandedRowKeys(prevKeys => {
                const newKeys = [...prevKeys];
                keys.forEach(key => {
                    if (!newKeys.includes(key)) {
                        newKeys.push(key);
                    }
                });
                return newKeys;
            });
        },
        [setExpandedRowKeys]
    );

    const unExpand = useCallback(
        (keys: string[]) => {
            setExpandedRowKeys(prevKeys => {
                const set = new Set(prevKeys);
                keys.forEach(key => {
                    if (set.has(key)) {
                        set.delete(key);
                    }
                });
                return [...set.values()];
            });
        },
        [setExpandedRowKeys]
    );

    const onExpand = useCallback(
        (expanded: boolean, record: BaseParam) => {
            const key = record.key;
            if (expanded) {
                expand([key]);
            } else {
                unExpand([key]);
            }
        }
        ,
        [expand, unExpand]
    );

    const handleValidationError = useCallback(
        (e: ApiParamsValidationError) => {
            const key = e.errorParam.key.split('.').shift();
            if (key === 'body') {
                const keys = extractKeyPath(e.errorParam.key);
                // 第一项是body、header、cookie这样的tab key，不需要
                expand(keys.slice(1));
            }
            setActiveKey(key);
            setTimeout(() => {
                window.console.log('validation');
                form.validateFields();
            }, 500);
        },
        [expand, form]
    );

    useEffect(
        () => {
            const {unsubscribe} = subscribeValidationError(handleValidationError);
            return unsubscribe;
        },
        [handleValidationError, subscribeValidationError]
    );

    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                title: '参数名',
                dataIndex: 'name',
                width: 200,
                render: (name: string, record, index) => (
                    <ParamNameField
                        name={name}
                        record={record}
                        index={index}
                        activeKey={activeKey}
                        sourcePath={sourcePath}
                    />
                ),
            },
            {
                title: '类型',
                width: 60,
                dataIndex: 'type',
            },
            {
                title: '是否必须',
                width: 60,
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
            {
                title: '说明',
                width: 150,
                dataIndex: 'description',
                render: description => (
                    <Typography.Paragraph ellipsis={{rows: 2, tooltip: {title: description}}}>
                        {description}
                    </Typography.Paragraph>
                ),
            },
            {
                title: '默认值',
                dataIndex: 'value',
                width: 150,
                render: (_, record) => {
                    return (
                        <ParamValueField
                            basePath={[...sourcePath, activeKey]}
                            record={record}
                        />
                    );
                },
            },
        ],
        [activeKey, sourcePath]
    );
    return (
        <Flex vertical>
            <Flex justify="space-between">
                <StyledTabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
                <Checkbox
                    checked={showRequired}
                    onChange={e => setShowRequired(e.target.checked)}
                    style={{alignItems: 'center'}}
                >
                    仅看必填项
                </Checkbox>
            </Flex>
            <Gap />
            <Form.Item name={sourcePath} style={{marginBottom: 0}}>
                <StyledTable
                    rowKey="key"
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys: expandedRowKeys,
                        expandIcon: ({expanded, onExpand, record}) => {
                            if (record?.children) {
                                return (
                                    <span onClick={e => onExpand(record, e)}>
                                        <StyledTriangle expanded={expanded} />
                                    </span>
                                );
                            }
                        },
                        onExpand: onExpand,
                    }}
                    dataSource={source?.[activeKey] || []}
                    columns={columns}
                    pagination={{hideOnSinglePage: true}}
                />
            </Form.Item>
        </Flex>
    );
};

export default ParamsConfig;
