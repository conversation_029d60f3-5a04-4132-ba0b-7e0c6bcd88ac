/* eslint-disable max-lines */
import {Form} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useMemo} from 'react';
import styled from '@emotion/styled';
import {IconSubtract} from '@/icons/mcp';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {useActiveTool} from '../hooks';
import {useToolParamsConfigContext, getParamValueFieldPath} from '../../Providers/ToolParamsConfigProvider';
import {isParamRelated} from './utils';

const ItemWrapper = styled.div<{activeKey: string, deep?: number}>`
    position: relative;
    padding-left: ${props => (
        props.activeKey === 'body'
            ? 16 + (props.deep + 1) * 12 + 'px'
            : '42px'
    )};
    .button-wrapper {
        position: absolute;
        top: -4px;
        left: -8px;
    }
`;

interface NameFieldProps{
    name: string;
    record: BaseParam;
    index: number;
    activeKey: string;
    sourcePath: Array<string|number>;
}

export const ParamNameField = ({name, record, activeKey, sourcePath}: NameFieldProps) => {
    const {getToolApiConfigData, addToolParam, removeToolParam} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const paramListPath = useMemo(
        () => ['tools', activeToolIndex, 'toolParams', 'toolParams'],
        [activeToolIndex]
    );
    const paramsList: BaseParam[] = Form.useWatch(paramListPath);
    const valueFieldPath = useMemo(
        () => {
            const allParams = getToolApiConfigData(activeToolIndex);
            return getParamValueFieldPath({
                basePath: [...sourcePath, activeKey],
                record,
                allParams,
            });
        },
        [getToolApiConfigData, activeToolIndex, sourcePath, activeKey, record]
    );
    const handleRelate = useCallback(
        (record: BaseParam) => {
            addToolParam(activeToolIndex, record);
        },
        [addToolParam, activeToolIndex]
    );
    const handleUnRelate = useCallback(
        (record: BaseParam) => {
            removeToolParam(activeToolIndex, record.key);
        },
        [removeToolParam, activeToolIndex]
    );

    const value = Form.useWatch(valueFieldPath);
    const canRelate = useCallback(
        (record: BaseParam) => {
            // 有值不能关联，用户的任何输入，都默认是该参数的常量值
            return record.canRelate && !value;
        },
        [value]
    );
    return (
        <ItemWrapper activeKey={activeKey} deep={record?.fieldPath?.length}>
            {
                isParamRelated(record.key, paramsList)
                    ? (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleUnRelate(record)}
                                type="text"
                                tooltip="取消关联"
                                icon={<IconSubtract />}
                            />
                        </span>
                    )
                // object不能给tool添加参数
                    : canRelate(record) && (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleRelate(record)}
                                type="text"
                                tooltip="关联"
                                icon={<IconPlus />}
                            />
                        </span>
                    )
            }
            {name}
        </ItemWrapper>
    );
};
