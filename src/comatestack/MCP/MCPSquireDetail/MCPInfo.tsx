import {Flex, Space, Typography, Divider} from 'antd';
import styled from '@emotion/styled';
import {useCallback} from 'react';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {IconCallCount, IconEye} from '@/icons/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import GoBackButton from '@/components/MCP/GoBackButton';
import PublishInfo from '@/components/MCP/PublishInfo';
import {MCPSpaceLink, MCPSquareLink} from '@/links/mcp';

const DescriptionWrapper = styled.div`
    background-color: #F8F8F8;
    padding: 10px 12px;
    color: #3C3C3C;
`;

const MCPInfo = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const workspaceId = useMCPWorkspaceId();

    const refresh = useCallback(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );

    const backUrl = workspaceId
        ? MCPSpaceLink.toUrl({workspaceId})
        : MCPSquareLink.toUrl();

    return (
        <Flex vertical gap={16}>
            <Flex gap={14} align="center" justify="space-between">
                <Flex align="center" gap={14}>
                    <GoBackButton size={24} url={backUrl} />
                    <MCPServerAvatar size={40} icon={mcpServer?.icon} radius={4} />
                    <Flex align="flex-start" vertical>
                        <Typography.Title level={4} style={{fontSize: 14}}>{mcpServer?.name}</Typography.Title>
                        <Flex align="center" style={{fontSize: 12, color: '#5C5C5C'}}>
                            {getServerTypeText(mcpServer?.serverSourceType)}
                            <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 10px'}} />
                            {mcpServer?.serverProtocolType}
                            <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 10px'}} />
                            <PublishInfo
                                username={mcpServer?.lastModifyUser}
                                time={mcpServer?.lastModifyTime}
                            />
                        </Flex>
                    </Flex>
                </Flex>
                <Flex>
                    <Space split={<Divider type="vertical" style={{borderColor: '#D9D9D9'}} />}>
                        <Flex align="center" gap={4} style={{color: '#666666'}}>
                            <IconEye style={{marginRight: 4}} />
                            {mcpServer?.serverMetrics?.viewCount ?? 0}
                            <IconCallCount style={{margin: '0 4px 0 12px'}} />
                            {mcpServer?.serverMetrics?.callCount ?? 0}
                        </Flex>
                        <MCPCollectButton
                            refresh={refresh}
                            favorite={mcpServer?.favorite}
                            serverId={mcpServerId}
                        />
                        <MCPSubscribeButton
                            id={mcpServerId}
                            workspaceId={mcpServer?.workspaceId}
                            showText
                        />
                    </Space>
                </Flex>
            </Flex>
            <DescriptionWrapper>
                {mcpServer?.description || '暂无描述'}
            </DescriptionWrapper>
            <Flex align="center" gap={40} style={{marginTop: '4px'}}>
                {mcpServer?.labels && mcpServer.labels.length > 0 && (
                    <DescriptionItem label="场景">
                        <TagGroup
                            labels={mcpServer.labels.map(
                                label => ({id: label.id, label: label.labelValue})
                            )}
                            color="light-purple"
                            maxNum={3}
                        />
                    </DescriptionItem>
                )}
                <DescriptionItem label="部门">
                    {mcpServer?.departmentName || '暂无部门信息'}
                </DescriptionItem>
                <DescriptionItem label="联系人">
                    <UserAvatarList users={mcpServer?.contacts ?? []} max={2} />
                </DescriptionItem>
            </Flex>
        </Flex>
    );
};

export default MCPInfo;
